import { loadDayjsPlugin, Toaster } from '@mayo/mayo-ui-beta/v2';
import { useSetAtom } from 'jotai';
import { FC, PropsWithChildren, Suspense, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { useGetUserInfo } from '@/hooks/api/user/useGetUserInfo';
import { useInitializeEmbedMode, useIsEmbedded } from '@/hooks/useEmbedMode';
import { userInfoAtom } from '@/lib/jotai/user';
import { sendMessage } from '@/lib/postRobot';
import { CookieNameEnum } from '@/types/enums/common/cookie';
import { getCookie } from '@/utils/cookie';

import Sidebar from './sidebar';

// 載入 dayjs plugin
loadDayjsPlugin();

/**
 * 路由變化事件訊息格式
 */
interface RouterChangeMessage {
  type: 'routerChange';
  payload: {
    pathname: string;
    search: string;
    hash: string;
    fullUrl: string;
  };
  timestamp: number;
}

const Layout: FC<PropsWithChildren> = ({ children }) => {
  const isLoggedIn = Boolean(getCookie(CookieNameEnum.AUTH_TOKEN));
  const location = useLocation();
  const isEmbedded = useIsEmbedded();

  const setUserInfo = useSetAtom(userInfoAtom);
  const fetchUserInfo = useGetUserInfo({ enabled: isLoggedIn });
  const initializeEmbedMode = useInitializeEmbedMode();

  // 初始化 embed mode（只在組件掛載時執行一次）
  useEffect(() => {
    initializeEmbedMode();
  }, [initializeEmbedMode]);

  useEffect(() => {
    if (fetchUserInfo.data) setUserInfo(fetchUserInfo.data);
  }, [fetchUserInfo.data, setUserInfo]);

  // 監聽路由變化並發送事件到父應用
  useEffect(() => {
    // 只在嵌入模式下發送路由變化事件
    if (!isEmbedded) return;

    // 確保在瀏覽器環境中執行
    if (typeof window === 'undefined') return;

    // 檢查父窗口是否存在且不等於當前窗口（避免在非嵌入環境中發送）
    if (!window.parent || window.parent === window) return;

    const sendRouterChangeEvent = async () => {
      try {
        const routerChangeMessage: RouterChangeMessage = {
          type: 'routerChange',
          payload: {
            pathname: location.pathname,
            search: location.search,
            hash: location.hash,
            fullUrl: `${location.pathname}${location.search}${location.hash}`,
          },
          timestamp: Date.now(),
        };

        await sendMessage(window.parent, 'routerChange', routerChangeMessage);
      } catch (error) {
        // 靜默處理通信錯誤，避免影響應用正常運行
        console.warn('Failed to send router change event to parent:', error);
      }
    };

    sendRouterChangeEvent();
  }, [location, isEmbedded]); // 依賴路由變化和嵌入模式狀態

  return (
    <div className="">
      <Suspense fallback={<div>Loading...</div>}>
        <Sidebar>{children}</Sidebar>
        <Toaster />
      </Suspense>
    </div>
  );
};

export default Layout;
