import * as postRobot from 'post-robot';

type PostRobotHandler = {
  cancel: () => void;
};

/**
 * 基本的跨窗口訊息介面
 */
export interface CrossWindowMessage {
  type?: string;
  payload?: unknown;
  timestamp?: number;
}

/**
 * 發送跨窗口訊息
 * @param targetWindow 目標窗口
 * @param messageType 訊息類型
 * @param messageData 訊息資料
 */
export const sendMessage = async <T extends object = CrossWindowMessage>(
  targetWindow: Window,
  messageType: string,
  messageData: T,
): Promise<void> => {
  try {
    const response = await postRobot.send(targetWindow, messageType, messageData);
    console.log('Message sent successfully:', response.data);
  } catch (error) {
    console.error('Error sending message:', error);
  }
};

/**
 * 設置跨窗口訊息監聽器
 * @param messageType 訊息類型
 * @param callback 回調函數
 */
export const setupListener = <T = CrossWindowMessage>(
  messageType: string,
  callback: (data: T) => void,
): PostRobotHandler => {
  const listener = postRobot.on(messageType, event => {
    callback(event.data as T);
    return Promise.resolve({ status: 'Received successfully' });
  });

  // Return the listener for potential cancellation
  return listener;
};
