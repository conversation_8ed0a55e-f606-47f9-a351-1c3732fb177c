# 路由變化通信機制

## 📋 概述

本文檔說明子應用（嵌入模式）與父應用之間的路由變化通信機制。當子應用在嵌入模式下運行時，會自動將路由變化事件發送到父應用，實現路由狀態同步。

## 🎯 功能特性

- ✅ 自動檢測路由變化
- ✅ 僅在嵌入模式下啟用
- ✅ 完整的錯誤處理
- ✅ 包含完整的路由資訊
- ✅ 支援所有路由變化場景

## 🔧 技術實現

### 核心組件

**位置**：`src/components/layout/index.tsx`

**關鍵技術**：
- React Router `useLocation` Hook
- Jotai `useIsEmbedded` 狀態管理
- PostRobot 跨窗口通信

### 事件資料格式

```typescript
interface RouterChangeMessage {
  type: 'routerChange';
  payload: {
    pathname: string;    // 路徑部分，如 "/shift-scheduler"
    search: string;      // 查詢參數，如 "?date=2024-01-15"
    hash: string;        // 錨點部分，如 "#calendar-view"
    fullUrl: string;     // 完整 URL 組合
  };
  timestamp: number;     // 事件發生時間戳
}
```

### 實現邏輯

```typescript
// 監聽路由變化並發送事件到父應用
useEffect(() => {
  // 安全檢查
  if (!isEmbedded) return;                    // 只在嵌入模式下執行
  if (typeof window === 'undefined') return;  // 確保瀏覽器環境
  if (!window.parent || window.parent === window) return; // 檢查父窗口

  const sendRouterChangeEvent = async () => {
    try {
      const routerChangeMessage: RouterChangeMessage = {
        type: 'routerChange',
        payload: {
          pathname: location.pathname,
          search: location.search,
          hash: location.hash,
          fullUrl: `${location.pathname}${location.search}${location.hash}`,
        },
        timestamp: Date.now(),
      };

      await sendMessage(window.parent, 'routerChange', routerChangeMessage);
    } catch (error) {
      console.warn('Failed to send router change event to parent:', error);
    }
  };

  sendRouterChangeEvent();
}, [location, isEmbedded]);
```

## 🌟 使用場景

### 觸發時機

1. **側邊欄導航**：用戶點擊側邊欄連結
2. **程式化導航**：使用 `navigate()` 函數
3. **瀏覽器操作**：前進/後退按鈕
4. **直接 URL 修改**：地址欄輸入新 URL
5. **初始載入**：頁面首次載入時

### 路由變化範例

#### 範例 1：基本頁面導航
```
從：https://app.example.com/
到：https://app.example.com/shift-scheduler

發送事件：
{
  type: 'routerChange',
  payload: {
    pathname: '/shift-scheduler',
    search: '',
    hash: '',
    fullUrl: '/shift-scheduler'
  },
  timestamp: 1704067200000
}
```

#### 範例 2：帶查詢參數的導航
```
從：https://app.example.com/employees
到：https://app.example.com/employees?department=kitchen&page=2

發送事件：
{
  type: 'routerChange',
  payload: {
    pathname: '/employees',
    search: '?department=kitchen&page=2',
    hash: '',
    fullUrl: '/employees?department=kitchen&page=2'
  },
  timestamp: 1704067260000
}
```

#### 範例 3：帶錨點的導航
```
從：https://app.example.com/settings
到：https://app.example.com/settings#notifications

發送事件：
{
  type: 'routerChange',
  payload: {
    pathname: '/settings',
    search: '',
    hash: '#notifications',
    fullUrl: '/settings#notifications'
  },
  timestamp: 1704067320000
}
```

## 🛡️ 錯誤處理

### 安全檢查機制

1. **嵌入模式檢查**：
   ```typescript
   if (!isEmbedded) return;
   ```

2. **瀏覽器環境檢查**：
   ```typescript
   if (typeof window === 'undefined') return;
   ```

3. **父窗口存在性檢查**：
   ```typescript
   if (!window.parent || window.parent === window) return;
   ```

4. **通信錯誤處理**：
   ```typescript
   try {
     await sendMessage(window.parent, 'routerChange', routerChangeMessage);
   } catch (error) {
     console.warn('Failed to send router change event to parent:', error);
   }
   ```

### 錯誤場景

- **非嵌入模式**：靜默跳過，不發送事件
- **父窗口不存在**：避免無效通信嘗試
- **PostRobot 通信失敗**：記錄警告但不影響應用運行
- **SSR 環境**：跳過瀏覽器特定操作

## 🔄 完整流程圖

```mermaid
graph TD
    A[用戶導航操作] --> B[React Router 更新 location]
    B --> C[useLocation 返回新的 location 物件]
    C --> D[Layout 組件重新渲染]
    D --> E[useEffect 檢測到 location 變化]
    E --> F{是否為嵌入模式?}
    F -->|否| G[不發送事件]
    F -->|是| H{瀏覽器環境?}
    H -->|否| G
    H -->|是| I{父窗口存在?}
    I -->|否| G
    I -->|是| J[構建 RouterChangeMessage]
    J --> K[通過 postRobot 發送到父應用]
    K --> L{發送成功?}
    L -->|是| M[父應用接收路由變化通知]
    L -->|否| N[記錄警告，繼續運行]
```

## 🚀 父應用接收範例

父應用可以這樣監聽路由變化事件：

```typescript
import { setupListener } from '@/lib/postRobot';

// 設置監聽器
const routerListener = setupListener('routerChange', (data) => {
  console.log('子應用路由變化:', data);
  
  // 根據子應用路由更新父應用狀態
  updateParentRouterState(data.payload);
});

// 清理監聽器（組件卸載時）
// routerListener.cancel();
```

## 📝 注意事項

1. **性能考量**：每次路由變化都會觸發通信，但開銷很小
2. **兼容性**：需要現代瀏覽器支援 PostMessage API
3. **安全性**：只在確認的嵌入環境中啟用
4. **調試**：可通過瀏覽器控制台查看通信日誌

## 🔗 相關文件

- [`src/lib/postRobot/index.ts`](../src/lib/postRobot/index.ts) - PostRobot 封裝
- [`src/hooks/useEmbedMode.ts`](../src/hooks/useEmbedMode.ts) - 嵌入模式管理
- [`src/lib/jotai/embedMode/index.tsx`](../src/lib/jotai/embedMode/index.tsx) - 嵌入模式狀態
