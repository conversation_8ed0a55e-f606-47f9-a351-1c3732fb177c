{"name": "mayo-pt-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "rm -rf dist && tsc -b && vite build", "build:wc": "rm -rf dist && tsc && vite build --config vite.config.wc.ts", "lint": "eslint .", "preview": "vite preview", "prepare": "husky", "config:generate": "node scripts/generate-configs.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@mayo/mayo-ui-beta": "^2.1.13", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "axios": "^1.10.0", "clsx": "^2.1.1", "dayjs": "^1.11.10", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "jotai": "^2.6.4", "jotai-immer": "^0.2.0", "lit": "^3.3.0", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "post-robot": "^8.0.32", "react": "^18.2.0", "react-big-calendar": "^1.10.3", "react-calendar-timeline": "0.30.0-beta.3", "react-cookie": "^8.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.59.0", "react-i18next": "^15.5.3", "react-router-dom": "^6.22.3", "react-window": "^1.8.11", "tailwind-merge": "^3.3.1", "zod": "^3.25.71"}, "devDependencies": {"@eslint/js": "^9.22.0", "@faker-js/faker": "^9.8.0", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/axios": "^0.14.4", "@types/lodash": "^4.17.20", "@types/node": "^24.0.7", "@types/post-robot": "^10.0.6", "@types/react": "^18.2.55", "@types/react-big-calendar": "^1.8.9", "@types/react-calendar-timeline": "^0.28.6", "@types/react-dom": "^18.2.19", "@types/react-i18next": "^8.1.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "msw": "^2.10.2", "postcss": "^8.5.6", "prettier": "3.6.0", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}, "packageManager": "pnpm@10.10.0+sha256.fa0f513aa8191764d2b6b432420788c270f07b4f999099b65bb2010eec702a30", "msw": {"workerDirectory": ["public"]}, "lint-staged": {"*.+(ts|tsx|jsx|js)": "eslint --cache --fix", "*.+(ts|tsx|jsx|js|json|css|md|mdx|html)": "prettier --write"}, "lint-staged:config": {"stash": false}}